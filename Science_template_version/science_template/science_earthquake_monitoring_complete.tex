% science_earthquake_monitoring_complete.tex
% Adapted from Science template for earthquake monitoring manuscript
% Original template: science_template.tex

%%%%%%%%%%%%%%%% START OF PREAMBLE %%%%%%%%%%%%%%%

% Basic setup. Authors shouldn't need to adjust these commands.
% It's annoying, but please do NOT strip these into a separate file.
% They need to be included in this .tex for our production software to work.

% Use the basic LaTeX article class, 12pt text
\documentclass[12pt]{article}

% Science uses Times font. If you don't have this installed (most LaTeX installations will be
% fine) or prefer the old Computer Modern fonts, comment out the following line
\usepackage{newtxtext,newtxmath}
% Depending on your LaTeX fonts installation, you might get better results with one or both of these:
%\usepackage{mathptmx}
%\usepackage{txfonts}

% Allow external graphics files
\usepackage{graphicx}

% Use US letter sized paper with 1 inch margins
\usepackage[letterpaper,margin=1in]{geometry}

% Double line spacing, including in captions
\linespread{1.5} % For some reason double spacing is 1.5, not 2.0!

% One space after each sentence
\frenchspacing

% Abstract formatting and spacing - no heading
\renewenvironment{abstract}
	{\quotation}
	{\endquotation}

% No date in the title section
\date{}

% Reference section heading
\renewcommand\refname{References and Notes}

% Figure and Table labels in bold
\makeatletter
\renewcommand{\fnum@figure}{\textbf{Figure \thefigure}}
\renewcommand{\fnum@table}{\textbf{Table \thetable}}
\makeatother

% Call the accompanying scicite.sty package.
% This formats citation numbers in Science style.
\usepackage{scicite}

% Provides the \url command, and fixes a crash if URLs or DOIs contain underscores
\usepackage{url}

% Additional packages needed for tables
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}

%%%%%%%%%%%% CUSTOM COMMANDS AND PACKAGES %%%%%%%%%%%%

% Authors can define simple custom commands e.g. as shortcuts to save on typing
% Use \newcommand (not \def) to avoid overwriting existing commands.
% Keep them as simple as possible and note the warning in the text below.

% Please DO NOT import additional external packages or .sty files.
% Those are unlikely to work with our conversion software and will cause problems later.
% Don't add any more \usepackage{} commands.


%%%%%%%%%%%%%%%% TITLE AND AUTHORS %%%%%%%%%%%%%%%%

% Title of the paper.
% Keep it short and understandable by any reader of Science.
% Avoid acronyms or jargon. Use sentence case.
\def\scititle{
	Resolving the signal ambiguity problem in satellite earthquake monitoring through environment-specific AI
}
% Store the title in a variable for reuse in the supplement (otherwise \maketitle deletes it)
\title{\bfseries \boldmath \scititle}

% Author and institution list.
% Institution numbers etc. should be hard-coded, do *not* use the \footnote command.
\author{
	% You can write out first names or use initials - either way is acceptable, but be consistent
	Pan~Xiong$^{1}$,
	Cheng~Long$^{2}$,
	Huiyu~Zhou$^{3}$,
	Roberto~Battiston$^{4,5}$,
	Angelo~De~Santis$^{6}$,
	Xuhui~Shen$^{7\ast}$\and
	% Additional lines of authors should be inserted using the \and command (not \\)
	% Institution list, in a slightly smaller font
	\small$^{1}$Institute of Earthquake Forecasting, China Earthquake Administration, Beijing, China.\and
	\small$^{2}$College of Computing and Data Science, Nanyang Technological University, Singapore.\and
	\small$^{3}$School of Computing and Mathematical Sciences, University of Leicester, Leicester, United Kingdom.\and
	\small$^{4}$Department of Physics, University of Trento, Trento, Italy.\and
	\small$^{5}$National Institute for Nuclear Physics, The Trento Institute for Fundamental Physics and Applications, Trento, Italy.\and
	\small$^{6}$Istituto Nazionale di Geofisica e Vulcanologia, Rome, Italy.\and
	\small$^{7}$National Space Science Center, Chinese Academy of Sciences, Beijing, China.\and
	% Identify at least one corresponding author, with contact email address
	\small$^\ast$Corresponding author. Email: <EMAIL>
}

%%%%%%%%%%%%%%%%% END OF PREAMBLE %%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%% START OF MAIN TEXT %%%%%%%%%%%%%%%
\begin{document} 

% Insert the title and author list
\maketitle

% Abstract, in bold
% There are strict length limits, and not all formats have abstracts.
% Consult the journal instructions to authors for details.
% Do not cite any references in the abstract.
\begin{abstract} \bfseries \boldmath
% Start with one or two sentences of background
For decades, satellite-based earthquake monitoring has been paralyzed by a fundamental paradox: the signal ambiguity problem. Microwave anomalies routinely detected before some earthquakes also occur without seismic consequence, while devastating earthquakes often strike without detectable precursors—rendering satellite monitoring largely ineffective for operational use.
% Then summarise the results of your observations, experiments, simulations etc.
Here we demonstrate that this long-standing barrier is not an inherent physical limitation but a methodological blind spot. By analyzing 346.56 million microwave brightness temperature measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, we reveal that reliable precursor signatures are environment-specific, not universal. Our knowledge-guided deep learning framework first classifies Earth's surface into five distinct zones, then discovers that marine environments achieve perfect detection through specific 89 GHz H-polarization patterns, while arid regions require entirely different frequency combinations. This environment-specific approach, implemented through our Weight-Enhanced Feature-Tailored Transformer (WE-FTT), achieves unprecedented discrimination between genuine precursors and environmental noise (MCC $\sim$0.84), a significant leap over conventional approaches.
% End with a statement of your main conclusions
By resolving the signal ambiguity problem, this work transforms satellite earthquake monitoring from an inconsistent research tool into a potentially operational system for disaster preparedness.
\end{abstract}


% The first paragraph of any Science paper does NOT have a heading
% Nor is it indented
\noindent
Earthquakes remain among the most devastating natural hazards, and reliable short-term prediction is a grand challenge in Earth science \cite{akhoondzadehMultiPrecursorsAnalysis2018}. While satellite-based microwave remote sensing offers unique potential for precursor detection due to its global, all-weather coverage \cite{pulinetsLithosphereAtmosphereIonosphere2011,wuGEOSSbasedThermalParameters2012}, its operational implementation has been stalled for decades by a fundamental challenge: the signal ambiguity problem \cite{troninRemoteSensingEarthquakes2006,tramutoliRobustSatelliteTechniques2013}.

The signal ambiguity problem has emerged as the central roadblock preventing operational satellite-based earthquake monitoring. Despite five decades of research investment and technological advancement, this fundamental challenge persists: identical-appearing microwave anomalies can precede either catastrophic earthquakes or benign environmental variations, while many major earthquakes occur without any distinguishable precursory signals. This ambiguity has led to an uncomfortable reality—while individual case studies report promising anomalies before specific earthquakes, systematic global analyses fail to establish reliable predictive patterns. The result is a field rich in anecdotal evidence but lacking operational capability \cite{gellerEarthquakesCannotPredicted1997,cuiSatelliteThermalInfrared2019,contiSystematicReviewMetaAnalysis2021,ciceroneSystematicCompilationEarthquake2009,jordanOperationalEarthquakeForecasting2011}.

Recent advances in machine learning have transformed many Earth observation applications \cite{reichsteinDeepLearningEarth2019,zhuDeepLearningRemote2017}, yet earthquake precursor detection remains stubbornly resistant to these approaches. The reason, we argue, lies not in the sophistication of the algorithms but in a fundamental methodological assumption: that a universal detection method can work across Earth's diverse surface environments. This "one-size-fits-all" approach amplifies the signal ambiguity problem by treating signals from ocean surfaces, dense forests, and arid deserts identically—ignoring the profound differences in how these environments modulate microwave emissions \cite{qiCharacteristicBackgroundMicrowave2023}.

To overcome this methodological roadblock, this paper proposes a comprehensive knowledge-guided AI framework designed specifically to resolve signal ambiguity through an environment-specific approach. Our work is grounded in Microwave Brightness Temperature (MBT) observations from the AMSR-2 satellite, a data source with unique advantages for this task. MBT offers all-weather monitoring, penetrates surface materials to potentially capture stress-induced subsurface changes, and its multi-frequency nature allows for depth-resolved analysis. Our framework leverages these advantages by first classifying Earth's surface into distinct environmental zones. Then, instead of relying on "black-box" learning, we use association rule mining to discover reliable, environment-specific precursor signatures (or "fingerprints"). This knowledge is then integrated into a novel Weight-Enhanced Feature-Tailored Transformer (WE-FTT) to guide its focus, fundamentally changing its ability to distinguish true precursors from environmental noise.

Our approach demonstrates three key innovations: (i) Environment-specific analysis—We classify Earth's surface into five distinct zones based on microwave radiative properties, enabling tailored precursor detection that accounts for diverse environmental conditions. This approach moves beyond uniform global methods to respect the physical differences in signal propagation across different landscapes. (ii) Knowledge-guided deep learning—We develop the Weight-Enhanced Feature-Tailored Transformer (WE-FTT), which integrates domain knowledge through pre-computed frequency importance weights derived from association rule mining. This design explicitly incorporates geophysical understanding into the model architecture rather than relying solely on end-to-end learning. (iii) Large-scale validation—We evaluate our approach on a substantial dataset comprising 346.56 million MBT measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, providing comprehensive evidence for the method's effectiveness across diverse conditions.

The primary objectives of this study are: (i) To demonstrate that the signal ambiguity problem can be resolved through environment-specific analysis, identifying reliable MBT frequency-polarization signatures for major (M$\ge$7.0) earthquakes across five distinct surface environments. (ii) To develop the WE-FTT model that operationalizes this insight, transforming ambiguous global signals into clear environment-specific indicators. (iii) To validate that accounting for environmental heterogeneity fundamentally improves earthquake precursor detection, providing a path toward operational satellite-based monitoring. Our study is grounded in a massive dataset, analyzing over 346 million MBT measurements from AMSR-2 spanning a decade (2013--2023) in relation to 154 major (M$\ge$7.0) earthquakes across globally distributed environmental zones. See Figure~\ref{fig:framework} for the framework.

\subsection*{Resolving signal ambiguity through environmental context}

Our analysis reveals a transformative insight: the signal ambiguity that has plagued satellite earthquake monitoring for decades can be effectively resolved through environment-specific analysis. By segmenting our global dataset into five distinct environmental zones and analyzing precursor patterns independently within each zone, we discovered that what appears as intractable noise in global analyses resolves into clear, consistent signatures when properly contextualized.

Figure~\ref{fig:signatures} demonstrates this breakthrough—each environmental zone exhibits unique, highly reliable frequency-polarization combinations for detecting seismic precursors. These environment-specific signatures achieve support values approaching or reaching 1.0, indicating near-perfect reliability within their respective environments. This finding fundamentally challenges the prevailing assumption that earthquake precursors should manifest uniformly across different surface types.

\subsection*{Multi-frequency microwave analysis reveals zone-dependent precursor patterns}

An initial exploratory analysis across the full earthquake catalog (M$\ge$4.8) revealed that consistent, statistically significant MBT anomalies were predominantly associated with major earthquakes (M$\ge$7.0). For smaller magnitudes, potential signals were indistinguishable from background noise at a global scale, confirming that the signal ambiguity problem is particularly acute for lower-magnitude events. We therefore focused our main analysis on M$\ge$7.0 earthquakes, where signals, though still ambiguous in conventional global analyses, possess sufficient strength to become distinguishable through our environment-specific framework.

Through comprehensive frequent itemset mining analysis, we have identified environment-specific anomaly signatures that achieve perfect or near-perfect support values, indicating robust seismic precursor detection capabilities across diverse landscape conditions.

In marine environments (Zone A), distinct pre-seismic anomalies were most effectively captured by the combination of 89 GHz H-polarization (anomaly range $\sim$219--253 K) and 36.5 GHz V-polarization (123--248 K), achieving perfect detection reliability (support = 1.0000). A secondary anomaly signature comprising 23.8 GHz H-polarization (177--235 K) and 36.5 GHz H-polarization (109--209 K) also showed near-perfect reliability (support = 0.9923).

Humid forest zones (Zone B) exhibited unique anomaly signatures, with optimal detection achieved through 89 GHz V-polarization (98--295 K) combined with 36.5 GHz V-polarization (140--297 K), yielding perfect support. Dry forest environments (Zone C) revealed seismic precursor signatures through 36.5 GHz H-polarization (121--207 K) combined with 10.65 GHz V-polarization (160--220 K), achieving near-perfect detection reliability.

Wetland zones (Zone D) demonstrated complex anomaly patterns with optimal detection involving various combinations incorporating 6.9 GHz H-polarization (73--166 K). In arid zones (Zone E), seismic precursor detection was optimized through the combination of 23.8GHz H-polarization (189.04-240.88K) and 36.5GHz V-polarization (103.18-246.57K), achieving perfect reliability.

\subsection*{Terrain-specific microwave sensitivity and optimal channel combinations}

To evaluate AMSR-2's multi-frequency microwave sensitivity to seismic anomalies, we conducted a comprehensive analysis of mean support values across diverse terrain types. The study categorized surface types into five zones based on vegetation coverage and soil moisture content: marine areas (Zone A), high vegetation-high soil moisture areas (Zone B), high vegetation-low soil moisture areas (Zone C), low vegetation-high soil moisture areas (Zone D), and low vegetation-low soil moisture areas (Zone E).

Analysis of mean support values revealed distinctive patterns across frequency bands and polarizations (see Table~\ref{tab:support_values}). At 6.9 GHz, horizontal polarization demonstrated varying effectiveness across terrain types, with highest support values in Zone D (0.9348, MBT: 166.36-655.35K), followed by Zone C (0.5716, MBT: 73.61-145.63K). The 89.0 GHz band showed unique capabilities in arid regions (Zone E), with horizontal polarization achieving 0.7380 (MBT: 64.46-215.3K), while Zone A reached its maximum sensitivity at this frequency (0.5078, MBT: 219.34-252.97K).

Multiple frequency combinations demonstrated enhanced detection capabilities in specific terrains. In Zone D, the combination of 36.5 GHz and 23.8 GHz horizontal polarization channels provided optimal detection with support values consistently above 0.90. Zone C showed best response to combined 36.5 GHz horizontal polarization and 10.65 GHz vertical polarization observations.

\subsection*{Model performance demonstrates breakthrough in signal disambiguation}

The proposed Weight-Enhanced Feature-Tailored Transformer (WE-FTT) architecture achieved superior performance across all evaluation metrics: MCC of 0.84, F1 score of 0.82, accuracy of 0.84, precision of 0.80, Cohen's Kappa of 0.82, and recall of 0.84. These results represent substantial improvements over the next best model (RandomForest), which achieved an MCC of 0.74.

The superior performance stems directly from integrating mined knowledge. By assigning pre-computed weights based on rules identifying consistent pre-seismic signatures, the WE-FTT effectively learns the subtle, environment-specific decision boundary between seismic and non-seismic states, enabling more reliable classification even when individual channel readings might otherwise be misleading.

\subsection*{Ablation study reveals architectural component hierarchy}

To understand the mechanistic basis of our model's superior performance, we systematically evaluated the contribution of each architectural component through comprehensive ablation experiments. Our ablation experiments across 18 variants revealed a clear hierarchy of component importance. The weight-enhanced dual projection pathway emerged as the cornerstone of our architecture---its removal caused catastrophic performance collapse (MCC: 0.84$\rightarrow$0.42), representing nearly 50\% degradation. This dramatic impact confirms that pre-computed frequency importance weights cannot be effectively learned through end-to-end training alone.

Feature projection proved similarly critical (41.4\% impact), while attention mechanisms showed more nuanced effects: residual connections (35\% impact) outweighed multi-head attention benefits (28\% impact), suggesting that stable gradient flow trumps attention diversity for geophysical signal processing. Surprisingly, position encoding---fundamental in language models---contributed minimally (<6\%), indicating that spatial relationships in gridded MBT data differ fundamentally from sequential dependencies.

Different architectural modifications produced distinct performance signatures across evaluation metrics. Weight projection removal caused asymmetric degradation: Cohen's Kappa plummeted 57.7\% while Precision dropped only 44.5\%, revealing that weight integration specifically enhances cross-class discrimination---crucial for distinguishing genuine precursors from environmental noise.

The ablation study thus transcends technical validation, revealing how architectural innovations can encode geophysical understanding into neural networks. By demonstrating that pre-computed weights based on seismic-frequency associations are irreplaceable by learned parameters, we establish a new paradigm for knowledge-guided deep learning in Earth observation.

\subsection*{From ambiguity to clarity: a paradigm shift in satellite earthquake monitoring}

Our findings demonstrate a robust pathway to resolving the signal ambiguity problem that has prevented operational satellite-based earthquake monitoring for half a century. The key insight is deceptively simple yet profound: reliable precursor signatures exist, but they are environment-specific rather than universal. By abandoning the search for a single, global precursor pattern and instead embracing environmental heterogeneity, we have transformed an ambiguous signal into a clear indicator of seismic preparation.

This paradigm shift has immediate practical implications. The Weight-Enhanced Feature-Tailored Transformer, achieving an MCC of 0.84 compared to 0.74 for the best conventional approach, demonstrates that accounting for environmental context is not merely an incremental improvement but a fundamental requirement for reliable detection. The 13.5\% performance gain represents the difference between a system plagued by false alarms and missed events versus one approaching operational reliability.

\subsection*{Physical mechanisms and theoretical implications}

The observed frequency-dependent anomaly patterns provide compelling evidence for multiple interconnected seismic preparation mechanisms operating across different environmental contexts. In wetland and arid zones, the broad anomaly detection ranges in low-frequency channels ($\Delta T$: 93-154K) strongly support the P-hole activation hypothesis, wherein stress-induced positive charge carriers migrate from deep crustal sources to the surface, modifying subsurface dielectric properties.

Marine environments exhibit distinct precursor signatures through high-frequency channels, consistent with theoretical models of thermal energy transfer from submarine fault zones. The 89 GHz H-polarization sensitivity likely captures microscale roughness changes induced by seafloor deformation or gas seepage, while 36.5 GHz V-polarization responds to broader thermal anomalies propagating through the water column.

Our multi-frequency, multi-polarization framework significantly advances beyond previous single-channel approaches. The association rule mining results demonstrate that certain channel combinations achieve perfect detection reliability only when analyzed together, suggesting nonlinear interactions between different penetration depths and polarization states.

\subsection*{Limitations and critical considerations}

Despite these advances, several factors warrant careful consideration when interpreting our results. The focus on M$\ge$7.0 earthquakes, while justified by preliminary analyses showing inconsistent MBT anomalies for smaller events in global-scale data, necessarily limits operational applicability. Regional studies with higher spatial resolution might reveal consistent precursors for moderate-magnitude events, particularly in areas with specific geological or environmental conditions that amplify pre-seismic signals.

Methodological constraints also merit acknowledgment. The K-means clustering required for association rule mining inevitably discretizes continuous MBT variations, potentially obscuring subtle nonlinear relationships. Additionally, potential confounding from extreme weather events, volcanic activity, or large-scale anthropogenic modifications remains incompletely characterized.

\subsection*{Advancing earthquake monitoring through physics-informed AI}

This work establishes a transformative approach to satellite-based earthquake monitoring by demonstrating that knowledge-guided architectural design fundamentally outperforms generic deep learning. The remarkable alignment between mined frequency importance and architectural component sensitivity confirms that our model has learned physically meaningful representations rather than statistical artifacts.

The implications extend beyond earthquake monitoring to broader challenges in Earth observation. By showing that pre-computed weights based on physical associations cannot be effectively replaced by learned parameters, we establish a new paradigm for geophysical AI: rather than treating neural networks as black boxes, we can encode domain knowledge directly into architectural innovations.

\subsection*{Toward operational implementation}

The resolution of the signal ambiguity problem opens immediate pathways toward operational deployment. A satellite-based monitoring system implementing our environment-specific approach could provide 2-20 day advance warning for major earthquakes, potentially saving thousands of lives annually. With over 3 billion people living in seismically active regions and annual earthquake damages exceeding \$40 billion globally, even modest improvements in short-term forecasting could yield enormous societal benefits.

Implementation would require: (1) real-time classification of global surface environments using existing satellite data, (2) continuous monitoring using the optimal frequency-polarization combinations identified for each zone, and (3) integration with ground-based monitoring systems for validation. Our framework provides the critical missing piece—a method to reliably distinguish genuine precursors from environmental noise—that has prevented such systems from being developed despite decades of effort.

% If your text is very short you might need to uncomment the following line to avoid
% layout problems with the figures and tables.
%\newpage


%%%%%%%%%%%%%%%% MAIN TEXT FIGURES %%%%%%%%%%%%%%%

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image1} % for an image file named image1.*
	% Pick an appropriate width - in print, figures are usually one or two columns wide, which can
	% be approximated by 0.3\textwidth or 0.6\textwidth respectively. Use appropriate label sizes.

	% Captions go below figures
	\caption{\textbf{Resolving signal ambiguity through environment-specific analysis: methodological framework.}
		The workflow illustrates the integration of surface type classification, earthquake-related data sampling, clustering analysis, association rule mining, and the Weight-Enhanced Feature-Tailored Transformer (WE-FTT) model. The WE-FTT incorporates mining-derived support values as pre-computed weights that are projected and multiplied with feature embeddings prior to attention computation, enabling enhanced seismic precursor detection across diverse environmental zones. This integrated approach enables resolution of the signal ambiguity problem by dynamically adjusting detection criteria based on environmental context.}
	\label{fig:framework} % give each figure a logical label name
\end{figure}

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image2} % for an image file named image2.*

	% Captions go below figures
	\caption{\textbf{Breakthrough in signal disambiguation: environment-specific precursor signatures achieving near-perfect reliability.}
		Primary combinations (blue) and secondary combinations (green) highlight varying sensitivities, with each bar labeled with the corresponding frequency-polarization combination (H: Horizontal, V: Vertical) to indicate the most effective configurations in each zone.}
	\label{fig:signatures} % give each figure a logical label name
\end{figure}


%%%%%%%%%%%%%%%% MAIN TEXT TABLES %%%%%%%%%%%%%%%

\begin{table} % Do NOT use \begin{table*}
	\centering
	% Captions go above tables
	\caption{\textbf{Segmented MBT ranges and corresponding mean support values for dual-polarization microwave observations across distinct terrain zones.}
		Range values are in Kelvin (K). The "---" symbol indicates not applicable. Bold values indicate the highest support values for each zone.}
	\label{tab:support_values} % give each table a logical label name

	\resizebox{0.95\textwidth}{!}{% Resize table to fit text width
	\renewcommand{\arraystretch}{1.0}
	\begin{tabular}{@{}llccccc@{}}
	\toprule
	      &   & \multicolumn{5}{c}{Mean Support Value (MBT segmentation range)} \\
	 \cmidrule(lr){3-7}
	\multicolumn{2}{l}{Freq. (GHz)}  & Zone A & Zone B & Zone C & Zone D & Zone E \\
	\midrule
	\multirow{2}{*}{6.9}
	    & H & 0.0074\,(41.8--168.1)  & 0.2688\,(163.7--388.2) & 0.5716\,(73.6--145.6)  & \textbf{0.9348}\,(166.4--655.4) & 0.4038\,(168.9--655.4) \\
	    & V & 0.0076\,(57.1--219.2)  & \textbf{0.3095}\,(252.2--388.1) & 0.6219\,(152.3--215.6) & \textbf{0.9219}\,(214.9--655.4) & 0.4038\,(218.6--655.4) \\
	\midrule
	\multirow{2}{*}{10.65}
	    & H & 0.0075\,(68.9--173.7)  & 0.2689\,(168.8--385.2) & 0.5578\,(79.0--130.8)  & \textbf{0.9306}\,(171.6--655.4) & 0.4038\,(173.4--655.4) \\
	    & V & 0.0075\,(121.3--223.9) & 0.2198\,(146.2--212.0) & 0.6220\,(159.6--220.4) & \textbf{0.9229}\,(219.6--655.4) & 0.4038\,(222.8--655.4) \\
	\midrule
	\multirow{2}{*}{23.8}
	    & H & \textbf{0.5124}\,(177.2--234.6) & 0.3016\,(104.4--182.5) & 0.4676\,(233.8--292.5) & \textbf{0.9044}\,(201.7--655.4) & 0.5075\,(189.0--240.9) \\
	    & V & 0.0183\,(241.0--655.3) & 0.3029\,(157.2--256.0) & 0.5043\,(257.4--302.6) & \textbf{0.8738}\,(201.7--655.4) & 0.5224\,(232.9--261.4) \\
	\midrule
	\multirow{2}{*}{36.5}
	    & H & 0.0227\,(109.3--209.3) & 0.1745\,(121.7--185.1) & \textbf{0.7028}\,(121.4--207.1) & \textbf{0.9713}\,(209.5--655.4) & 0.4052\,(210.9--655.4) \\
	    & V & 0.0228\,(122.7--247.6) & 0.3054\,(139.6--297.2) & 0.5412\,(148.4--244.3) & \textbf{0.8738}\,(139.5--301.6) & 0.4079\,(103.2--246.6) \\
	\midrule
	\multirow{2}{*}{89.0}
	    & H & \textbf{0.5078}\,(219.3--253.0) & 0.3053\,(94.8--212.0)  & 0.5567\,(217.3--250.0) & \textbf{0.8738}\,(80.6--295.9)  & \textbf{0.7380}\,(64.5--215.3)  \\
	    & V & 0.0179\,(270.7--655.4) & 0.3054\,(98.0--295.4)  & 0.5741\,(260.8--298.5) & \textbf{0.8738}\,(82.8--298.8)  & 0.2657\,(65.2--231.2)  \\
	\bottomrule
	\end{tabular}
	} % End resizebox
\end{table}



%%%%%%%%%%%%%%%% REFERENCES %%%%%%%%%%%%%%%

\clearpage % Clear all remaining figures and tables then start a new page

% The list of references goes after the main text and before the acknowledgements
% When preparing an initial submission, we recommend you use BibTeX, like this:
%
\bibliography{references} % for a file named references.bib
\bibliographystyle{sciencemag}


%%%%%%%%%%%%%%%% ACKNOWLEDGEMENTS %%%%%%%%%%%%%%%

\section*{Acknowledgments}
We thank the Japan Aerospace Exploration Agency (JAXA) for providing AMSR-2 data and the United States Geological Survey (USGS) for earthquake catalog data. We acknowledge the computational resources provided by the National Space Science Center, Chinese Academy of Sciences.

\paragraph*{Funding:}
This work was supported by the National Natural Science Foundation of China (grant numbers 42174081, 42074082), the Strategic Priority Research Program of the Chinese Academy of Sciences (grant number XDA17010301), and the Dragon 5 Cooperation Programme (project ID 59236). P.X. was supported by the China Earthquake Administration. C.L. was supported by the Nanyang Technological University. H.Z. was supported by the University of Leicester. R.B. was supported by the University of Trento and INFN. A.D.S. was supported by INGV. X.S. was supported by the Chinese Academy of Sciences.

\paragraph*{Author contributions:}
P.X. conceived the study, developed the methodology, performed the analysis, and wrote the manuscript. C.L. contributed to the machine learning model development and validation. H.Z. provided expertise in computer vision and pattern recognition. R.B. contributed to the geophysical interpretation and validation. A.D.S. provided expertise in earthquake precursor research and data interpretation. X.S. supervised the project, provided scientific guidance, and contributed to manuscript writing. All authors reviewed and approved the final manuscript.

\paragraph*{Competing interests:}
There are no competing interests to declare.

\paragraph*{Data and materials availability:}
AMSR-2 microwave brightness temperature data are available from the Japan Aerospace Exploration Agency (JAXA) Global Change Observation Mission (GCOM) data portal. Earthquake catalog data are available from the United States Geological Survey (USGS) Earthquake Hazards Program. The WE-FTT model code and processed datasets supporting the conclusions of this article will be made available upon publication through a public repository with DOI assignment. Specific data analysis scripts and model weights are available from the corresponding author upon reasonable request.


%%%%%%%%%%%%%%%%% END OF MAIN TEXT %%%%%%%%%%%%%%%%
\end{document}
